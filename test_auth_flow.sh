#!/bin/bash

# Test script for the new authentication system
# This script tests the complete authentication flow

set -e

echo "🧪 Testing Colony Authentication System"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

BASE_URL="http://localhost:3000"
TEST_PASSWORD="test123"

echo -e "${YELLOW}📝 Testing authentication endpoints...${NC}"

# Test 1: Try to get token without password (should fail)
echo -e "\n${YELLOW}Test 1: Token request without password${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/auth/token \
  -H "Content-Type: application/json" \
  -d '{}' -o /tmp/auth_response.json)

if [ "$RESPONSE" = "400" ] || [ "$RESPONSE" = "422" ]; then
    echo -e "${GREEN}✅ Correctly rejected token request without password${NC}"
else
    echo -e "${RED}❌ Expected 400/422 but got $RESPONSE${NC}"
    cat /tmp/auth_response.json
fi

# Test 2: Try to get token with wrong password (should fail)
echo -e "\n${YELLOW}Test 2: Token request with wrong password${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/auth/token \
  -H "Content-Type: application/json" \
  -d '{"password": "wrong_password"}' -o /tmp/auth_response.json)

if [ "$RESPONSE" = "401" ]; then
    echo -e "${GREEN}✅ Correctly rejected token request with wrong password${NC}"
else
    echo -e "${RED}❌ Expected 401 but got $RESPONSE${NC}"
    cat /tmp/auth_response.json
fi

# Test 3: Get token with correct password (should succeed)
echo -e "\n${YELLOW}Test 3: Token request with correct password${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/auth/token \
  -H "Content-Type: application/json" \
  -d "{\"password\": \"$TEST_PASSWORD\"}" -o /tmp/auth_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Successfully obtained token with correct password${NC}"
    TOKEN=$(cat /tmp/auth_response.json | jq -r '.token')
    if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
        echo -e "${GREEN}✅ Token is valid: ${TOKEN:0:20}...${NC}"
    else
        echo -e "${RED}❌ Token is null or empty${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ Expected 200 but got $RESPONSE${NC}"
    cat /tmp/auth_response.json
    exit 1
fi

# Test 4: Try to access protected endpoint without token (should fail)
echo -e "\n${YELLOW}Test 4: Access protected endpoint without token${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X GET $BASE_URL/api/v1/pods -o /tmp/pods_response.json)

if [ "$RESPONSE" = "401" ]; then
    echo -e "${GREEN}✅ Correctly rejected request without token${NC}"
else
    echo -e "${RED}❌ Expected 401 but got $RESPONSE${NC}"
    cat /tmp/pods_response.json
fi

# Test 5: Try to access protected endpoint with valid token (should succeed)
echo -e "\n${YELLOW}Test 5: Access protected endpoint with valid token${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X GET $BASE_URL/api/v1/pods \
  -H "Authorization: Bearer $TOKEN" -o /tmp/pods_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Successfully accessed protected endpoint with valid token${NC}"
else
    echo -e "${RED}❌ Expected 200 but got $RESPONSE${NC}"
    cat /tmp/pods_response.json
fi

# Test 6: Test legacy endpoint (should work but create token without password verification)
echo -e "\n${YELLOW}Test 6: Legacy token endpoint${NC}"
RESPONSE=$(curl -s -w "%{http_code}" -X POST $BASE_URL/auth/token/legacy \
  -H "Content-Type: application/json" -o /tmp/legacy_response.json)

if [ "$RESPONSE" = "200" ]; then
    echo -e "${GREEN}✅ Legacy endpoint works${NC}"
    LEGACY_TOKEN=$(cat /tmp/legacy_response.json | jq -r '.token')
    
    # Try to use legacy token on protected endpoint (should fail due to password verification requirement)
    LEGACY_RESPONSE=$(curl -s -w "%{http_code}" -X GET $BASE_URL/api/v1/pods \
      -H "Authorization: Bearer $LEGACY_TOKEN" -o /tmp/legacy_pods_response.json)
    
    if [ "$LEGACY_RESPONSE" = "401" ]; then
        echo -e "${GREEN}✅ Legacy token correctly rejected by protected endpoints${NC}"
    else
        echo -e "${RED}❌ Legacy token should be rejected but got $LEGACY_RESPONSE${NC}"
        cat /tmp/legacy_pods_response.json
    fi
else
    echo -e "${RED}❌ Legacy endpoint failed with $RESPONSE${NC}"
    cat /tmp/legacy_response.json
fi

echo -e "\n${GREEN}🎉 Authentication system tests completed!${NC}"
echo -e "${YELLOW}Summary:${NC}"
echo -e "- ✅ Password-protected token creation works"
echo -e "- ✅ Wrong passwords are rejected"
echo -e "- ✅ Valid tokens allow access to protected endpoints"
echo -e "- ✅ Missing tokens are rejected"
echo -e "- ✅ Legacy tokens without password verification are rejected"

# Cleanup
rm -f /tmp/auth_response.json /tmp/pods_response.json /tmp/legacy_response.json /tmp/legacy_pods_response.json

echo -e "\n${GREEN}✅ All tests passed! The authentication system is working correctly.${NC}"
